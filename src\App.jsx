import React, { useEffect } from 'react'

function App() {
  useEffect(() => {
    // Scroll animations
    const observerOptions = {
      threshold: 0.1,
      rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver((entries) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          entry.target.classList.add('animate-in');
        } else {
          entry.target.classList.remove('animate-in');
        }
      });
    }, observerOptions);

    // Observe all animatable elements
    const animatableElements = document.querySelectorAll('.info-card, .education-item, .section-title, .header');
    animatableElements.forEach(el => observer.observe(el));

    // Parallax scroll effect
    const handleScroll = () => {
      const scrolled = window.pageYOffset;
      const header = document.querySelector('.header');
      const particles = document.querySelector('.particles-layer');

      if (header) {
        header.style.transform = `translateY(${scrolled * 0.3}px)`;
      }
      if (particles) {
        particles.style.transform = `translateY(${scrolled * 0.1}px) rotate(${scrolled * 0.1}deg)`;
      }
    };

    window.addEventListener('scroll', handleScroll);

    return () => {
      observer.disconnect();
      window.removeEventListener('scroll', handleScroll);
    };
  }, []);
  const personalInfo = {
    name: "Saputra Pramahkota Hati",
    class: "10 TKJ A",
    address: "Rt 08, Rw 04, dusun kaliwowo, desa kedunggalar, Ngawi, Jawa Timur",
    phone: "+62 878-5062-9480",
    email: "<EMAIL>",
    birthDate: "19 Juni 2008",
    hobby: "Programming, Gaming, Reading"
  }

  const educationHistory = [
    {
      school: "SMK Pgri 1 Ngawi",
      level: "Sekolah Menengah Kejuruan",
      major: "Teknik Komputer dan Jaringan (TKJ)",
      period: "2024 - Sekarang",
      status: "Sedang Bersekolah"
    },
    {
      school: "SMP Negeri 1 Kedunggalar",
      level: "Sekolah Menengah Pertama",
      major: "-",
      period: "2021 - 2024",
      status: "Lulus"
    },
    {
      school: "SD Negeri 3 Kedunggalar",
      level: "Sekolah Dasar",
      major: "-",
      period: "2015 - 2021",
      status: "Lulus"
    }
  ]

  return (
    <div className="app-container">
      {/* Floating Particles Layer */}
      <div className="particles-layer"></div>

      <div className="container scroll-animate">
        {/* Header Section */}
        <div className="header scroll-animate">
          <div className="profile-image">
            SP
          </div>
          <h1 className="name">{personalInfo.name}</h1>
          <p className="title">Siswa Kelas {personalInfo.class}</p>
        </div>

      {/* Content Section */}
      <div className="content">
        {/* Personal Information */}
        <div className="section scroll-animate">
          <h2 className="section-title scroll-animate">
            <i className="fas fa-user-circle"></i>
            Informasi Pribadi
          </h2>
          <div className="info-grid">
            <div className="info-card scroll-animate" data-delay="0.1">
              <div className="info-label">
                <i className="fas fa-user" style={{color: '#667eea'}}></i>
                Nama Lengkap
              </div>
              <div className="info-value">{personalInfo.name}</div>
            </div>
            <div className="info-card scroll-animate" data-delay="0.2">
              <div className="info-label">
                <i className="fas fa-graduation-cap" style={{color: '#764ba2'}}></i>
                Kelas
              </div>
              <div className="info-value">{personalInfo.class}</div>
            </div>
            <div className="info-card scroll-animate" data-delay="0.3">
              <div className="info-label">
                <i className="fas fa-calendar" style={{color: '#f093fb'}}></i>
                Tanggal Lahir
              </div>
              <div className="info-value">{personalInfo.birthDate}</div>
            </div>
            <div className="info-card scroll-animate" data-delay="0.4">
              <div className="info-label">
                <i className="fas fa-map-marker-alt" style={{color: '#f5576c'}}></i>
                Alamat
              </div>
              <div className="info-value">{personalInfo.address}</div>
            </div>
            <div className="info-card scroll-animate" data-delay="0.5">
              <div className="info-label">
                <i className="fas fa-phone" style={{color: '#4facfe'}}></i>
                No. Telepon
              </div>
              <div className="info-value">{personalInfo.phone}</div>
            </div>
            <div className="info-card scroll-animate" data-delay="0.6">
              <div className="info-label">
                <i className="fas fa-envelope" style={{color: '#00f2fe'}}></i>
                Email
              </div>
              <div className="info-value">{personalInfo.email}</div>
            </div>
            <div className="info-card scroll-animate" data-delay="0.7">
              <div className="info-label">
                <i className="fas fa-heart" style={{color: '#ff9a9e'}}></i>
                Hobi
              </div>
              <div className="info-value">{personalInfo.hobby}</div>
            </div>
          </div>
        </div>

        {/* Education History */}
        <div className="section scroll-animate">
          <h2 className="section-title scroll-animate">
            <i className="fas fa-school"></i>
            Riwayat Pendidikan
          </h2>
          {educationHistory.map((education, index) => (
            <div key={index} className="education-item scroll-animate" data-delay={`${0.2 + index * 0.2}`}>
              <div className="education-school">
                <i className="fas fa-school" style={{color: index === 0 ? '#667eea' : index === 1 ? '#764ba2' : '#f093fb'}}></i>
                {education.school}
              </div>
              <div className="education-details">
                <strong>{education.level}</strong>
                {education.major !== "-" && (
                  <>
                    <br />
                    <span>Jurusan: {education.major}</span>
                  </>
                )}
                <br />
                <span>Periode: {education.period}</span>
                <br />
                <span className={`status-badge ${education.status === 'Sedang Bersekolah' ? 'status-current' : 'status-completed'}`}>
                  {education.status}
                </span>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
    </div>
  )
}

export default App
