import React from 'react'

function App() {
  const personalInfo = {
    name: "Saputra Pramahkota Hati",
    class: "10 TKJ A",
    address: "Jl. Con<PERSON>h <PERSON>t No. 123, Kota Con<PERSON>h, <PERSON><PERSON><PERSON>",
    phone: "+62 812-3456-7890",
    email: "<EMAIL>",
    birthDate: "15 Januari 2008",
    hobby: "Programming, Gaming, Reading"
  }

  const educationHistory = [
    {
      school: "SMK Negeri 1 Teknologi",
      level: "Sekolah Menengah Kejuruan",
      major: "Teknik Komputer dan <PERSON> (TKJ)",
      period: "2023 - Sekarang",
      status: "Sedang Bersekolah"
    },
    {
      school: "SMP Negeri 5 Kota",
      level: "Sekolah Menengah Pertama",
      major: "-",
      period: "2020 - 2023",
      status: "Lulus"
    },
    {
      school: "SD Negeri 10 Kota",
      level: "Sekolah Dasar",
      major: "-",
      period: "2014 - 2020",
      status: "Lulus"
    }
  ]

  return (
    <div className="container">
      {/* Header Section */}
      <div className="header">
        <div className="profile-image">
          SP
        </div>
        <h1 className="name">{personalInfo.name}</h1>
        <p className="title">Siswa Kelas {personalInfo.class}</p>
      </div>

      {/* Content Section */}
      <div className="content">
        {/* Personal Information */}
        <div className="section">
          <h2 className="section-title">Informasi Pribadi</h2>
          <div className="info-grid">
            <div className="info-card">
              <div className="info-label">Nama Lengkap</div>
              <div className="info-value">{personalInfo.name}</div>
            </div>
            <div className="info-card">
              <div className="info-label">Kelas</div>
              <div className="info-value">{personalInfo.class}</div>
            </div>
            <div className="info-card">
              <div className="info-label">Tanggal Lahir</div>
              <div className="info-value">{personalInfo.birthDate}</div>
            </div>
            <div className="info-card">
              <div className="info-label">Alamat</div>
              <div className="info-value">{personalInfo.address}</div>
            </div>
            <div className="info-card">
              <div className="info-label">No. Telepon</div>
              <div className="info-value">{personalInfo.phone}</div>
            </div>
            <div className="info-card">
              <div className="info-label">Email</div>
              <div className="info-value">{personalInfo.email}</div>
            </div>
            <div className="info-card">
              <div className="info-label">Hobi</div>
              <div className="info-value">{personalInfo.hobby}</div>
            </div>
          </div>
        </div>

        {/* Education History */}
        <div className="section">
          <h2 className="section-title">Riwayat Pendidikan</h2>
          {educationHistory.map((education, index) => (
            <div key={index} className="education-item">
              <div className="education-school">{education.school}</div>
              <div className="education-details">
                <strong>{education.level}</strong>
                {education.major !== "-" && (
                  <>
                    <br />
                    <span>Jurusan: {education.major}</span>
                  </>
                )}
                <br />
                <span>Periode: {education.period}</span>
                <br />
                <span>Status: {education.status}</span>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  )
}

export default App
