<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Biodata - Saputra Pramahkota Hati</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 25%, #f093fb 50%, #f5576c 75%, #4facfe 100%);
            background-size: 400% 400%;
            animation: gradientShift 15s ease infinite;
            min-height: 100vh;
            color: #334155;
            padding: 20px;
        }

        @keyframes gradientShift {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }

        .container {
            max-width: 900px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 24px;
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
            overflow: hidden;
            animation: fadeInUp 0.8s ease-out;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
            color: white;
            padding: 60px 40px;
            text-align: center;
            position: relative;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
        }

        .header::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
            animation: float 6s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(180deg); }
        }

        .profile-image {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            margin: 0 auto 20px;
            background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 50%, #fecfef 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 48px;
            color: #667eea;
            border: 4px solid rgba(255, 255, 255, 0.5);
            position: relative;
            z-index: 1;
            font-weight: 700;
            box-shadow: 0 8px 25px rgba(255, 154, 158, 0.4);
        }

        .name {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 10px;
            position: relative;
            z-index: 1;
        }

        .title {
            font-size: 1.2rem;
            font-weight: 400;
            opacity: 0.9;
            position: relative;
            z-index: 1;
        }

        .content {
            padding: 40px;
        }

        .section {
            margin-bottom: 40px;
            animation: slideInLeft 0.6s ease-out;
            animation-fill-mode: both;
        }

        .section:nth-child(2) { animation-delay: 0.2s; }
        .section:nth-child(3) { animation-delay: 0.4s; }

        @keyframes slideInLeft {
            from {
                opacity: 0;
                transform: translateX(-30px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        .section-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: #475569;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .section-title::before {
            content: '';
            width: 4px;
            height: 24px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            border-radius: 2px;
        }

        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 20px;
        }

        .info-card {
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(255, 255, 255, 0.7) 100%);
            padding: 20px;
            border-radius: 16px;
            border: 1px solid rgba(102, 126, 234, 0.2);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
            backdrop-filter: blur(10px);
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.1);
        }

        .info-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 3px;
            background: linear-gradient(90deg, #667eea 0%, #764ba2 25%, #f093fb 50%, #f5576c 75%, #4facfe 100%);
        }

        .info-card:hover {
            transform: translateY(-5px) scale(1.02);
            box-shadow: 0 15px 30px rgba(102, 126, 234, 0.3);
            border-color: rgba(102, 126, 234, 0.4);
        }

        .info-label {
            font-size: 0.875rem;
            font-weight: 500;
            color: #667eea;
            margin-bottom: 8px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .info-value {
            font-size: 1.1rem;
            font-weight: 500;
            color: #334155;
            line-height: 1.4;
        }

        .education-item {
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(255, 255, 255, 0.7) 100%);
            padding: 24px;
            border-radius: 16px;
            border: 1px solid rgba(245, 87, 108, 0.2);
            margin-bottom: 16px;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
            backdrop-filter: blur(10px);
            box-shadow: 0 4px 15px rgba(245, 87, 108, 0.1);
        }

        .education-item::before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            width: 5px;
            height: 100%;
            background: linear-gradient(135deg, #f5576c 0%, #f093fb 50%, #764ba2 100%);
        }

        .education-item:hover {
            transform: translateX(8px) scale(1.02);
            box-shadow: 0 15px 30px rgba(245, 87, 108, 0.3);
            border-color: rgba(245, 87, 108, 0.4);
        }

        .education-school {
            font-size: 1.2rem;
            font-weight: 600;
            color: #f5576c;
            margin-bottom: 8px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .education-details {
            color: #64748b;
            font-size: 0.95rem;
            line-height: 1.6;
        }

        .status-badge {
            display: inline-block;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 500;
            margin-top: 8px;
        }

        .status-current {
            background: linear-gradient(135deg, #a7f3d0, #6ee7b7);
            color: #065f46;
            box-shadow: 0 2px 8px rgba(110, 231, 183, 0.3);
        }

        .status-completed {
            background: linear-gradient(135deg, #bfdbfe, #93c5fd);
            color: #1e3a8a;
            box-shadow: 0 2px 8px rgba(147, 197, 253, 0.3);
        }

        @media (max-width: 768px) {
            .container {
                margin: 10px;
                border-radius: 16px;
            }

            .header {
                padding: 40px 20px;
            }

            .name {
                font-size: 2rem;
            }

            .content {
                padding: 20px;
            }

            .info-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header Section -->
        <div class="header">
            <div class="profile-image">
                SP
            </div>
            <h1 class="name">Saputra Pramahkota Hati</h1>
            <p class="title">Siswa Kelas 10 TKJ A</p>
        </div>

        <!-- Content Section -->
        <div class="content">
            <!-- Personal Information -->
            <div class="section">
                <h2 class="section-title">Informasi Pribadi</h2>
                <div class="info-grid">
                    <div class="info-card">
                        <div class="info-label">
                            <i class="fas fa-user" style="color: #667eea;"></i>
                            Nama Lengkap
                        </div>
                        <div class="info-value">Saputra Pramahkota Hati</div>
                    </div>
                    <div class="info-card">
                        <div class="info-label">
                            <i class="fas fa-graduation-cap" style="color: #764ba2;"></i>
                            Kelas
                        </div>
                        <div class="info-value">10 TKJ A</div>
                    </div>
                    <div class="info-card">
                        <div class="info-label">
                            <i class="fas fa-calendar" style="color: #f093fb;"></i>
                            Tanggal Lahir
                        </div>
                        <div class="info-value">15 Januari 2008</div>
                    </div>
                    <div class="info-card">
                        <div class="info-label">
                            <i class="fas fa-map-marker-alt" style="color: #f5576c;"></i>
                            Alamat
                        </div>
                        <div class="info-value">Jl. Merdeka No. 123, Kelurahan Sukamaju, Kecamatan Teknologi, Kota Bandung, Jawa Barat 40123</div>
                    </div>
                    <div class="info-card">
                        <div class="info-label">
                            <i class="fas fa-phone" style="color: #4facfe;"></i>
                            No. Telepon
                        </div>
                        <div class="info-value">+62 812-3456-7890</div>
                    </div>
                    <div class="info-card">
                        <div class="info-label">
                            <i class="fas fa-envelope" style="color: #00f2fe;"></i>
                            Email
                        </div>
                        <div class="info-value"><EMAIL></div>
                    </div>
                    <div class="info-card">
                        <div class="info-label">
                            <i class="fas fa-heart" style="color: #ff9a9e;"></i>
                            Hobi
                        </div>
                        <div class="info-value">Programming, Gaming, Membaca, Olahraga</div>
                    </div>
                </div>
            </div>

            <!-- Education History -->
            <div class="section">
                <h2 class="section-title">Riwayat Pendidikan</h2>

                <div class="education-item">
                    <div class="education-school">
                        <i class="fas fa-school" style="color: #667eea;"></i>
                        SMK Negeri 1 Teknologi Bandung
                    </div>
                    <div class="education-details">
                        <strong>Sekolah Menengah Kejuruan</strong><br>
                        <span>Jurusan: Teknik Komputer dan Jaringan (TKJ)</span><br>
                        <span>Periode: 2023 - Sekarang</span><br>
                        <span class="status-badge status-current">Sedang Bersekolah</span>
                    </div>
                </div>

                <div class="education-item">
                    <div class="education-school">
                        <i class="fas fa-school" style="color: #764ba2;"></i>
                        SMP Negeri 5 Bandung
                    </div>
                    <div class="education-details">
                        <strong>Sekolah Menengah Pertama</strong><br>
                        <span>Periode: 2020 - 2023</span><br>
                        <span class="status-badge status-completed">Lulus</span>
                    </div>
                </div>

                <div class="education-item">
                    <div class="education-school">
                        <i class="fas fa-school" style="color: #f093fb;"></i>
                        SD Negeri 10 Sukamaju
                    </div>
                    <div class="education-details">
                        <strong>Sekolah Dasar</strong><br>
                        <span>Periode: 2014 - 2020</span><br>
                        <span class="status-badge status-completed">Lulus</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Add some interactive animations
        document.addEventListener('DOMContentLoaded', function() {
            // Animate cards on scroll
            const observerOptions = {
                threshold: 0.1,
                rootMargin: '0px 0px -50px 0px'
            };

            const observer = new IntersectionObserver(function(entries) {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.style.opacity = '1';
                        entry.target.style.transform = 'translateY(0)';
                    }
                });
            }, observerOptions);

            // Observe all cards
            document.querySelectorAll('.info-card, .education-item').forEach(card => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(20px)';
                card.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
                observer.observe(card);
            });

            // Add click effect to cards
            document.querySelectorAll('.info-card').forEach(card => {
                card.addEventListener('click', function() {
                    this.style.transform = 'scale(0.98)';
                    setTimeout(() => {
                        this.style.transform = 'translateY(-2px)';
                    }, 150);
                });
            });
        });
    </script>
</body>
</html>
