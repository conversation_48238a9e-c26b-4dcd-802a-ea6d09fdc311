<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Biodata - Saputra Pramahkota Hati</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 25%, #f093fb 50%, #f5576c 75%, #4facfe 100%);
            background-size: 400% 400%;
            animation: gradientShift 15s ease infinite;
            min-height: 100vh;
            color: #334155;
            padding: 20px;
        }

        @keyframes gradientShift {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }

        .container {
            max-width: 900px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 24px;
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
            overflow: hidden;
            animation: fadeInUp 0.8s ease-out, containerFloat 6s ease-in-out infinite;
            position: relative;
        }

        .container::before {
            content: '';
            position: absolute;
            top: -2px;
            left: -2px;
            right: -2px;
            bottom: -2px;
            background: linear-gradient(45deg, #667eea, #764ba2, #f093fb, #f5576c, #4facfe, #00f2fe);
            background-size: 400% 400%;
            border-radius: 26px;
            z-index: -1;
            animation: borderGlow 3s ease-in-out infinite;
        }

        @keyframes containerFloat {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            33% { transform: translateY(-5px) rotate(0.5deg); }
            66% { transform: translateY(5px) rotate(-0.5deg); }
        }

        @keyframes borderGlow {
            0%, 100% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
            color: white;
            padding: 60px 40px;
            text-align: center;
            position: relative;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
        }

        .header::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
            animation: float 6s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(180deg); }
        }

        .profile-image {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            margin: 0 auto 20px;
            background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 50%, #fecfef 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 48px;
            color: #667eea;
            border: 4px solid rgba(255, 255, 255, 0.5);
            position: relative;
            z-index: 1;
            font-weight: 700;
            box-shadow: 0 8px 25px rgba(255, 154, 158, 0.4);
            animation:
                profilePulse 3s ease-in-out infinite,
                profileFloat 4s ease-in-out infinite,
                profileGlow 2s ease-in-out infinite alternate;
            cursor: pointer;
            transition: all 0.5s cubic-bezier(0.68, -0.55, 0.265, 1.55);
            overflow: hidden;
        }

        .profile-image::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: conic-gradient(from 0deg, #667eea, #764ba2, #f093fb, #f5576c, #4facfe, #00f2fe, #667eea);
            animation: profileSpinBorder 4s linear infinite;
            z-index: -1;
        }

        .profile-image::after {
            content: '';
            position: absolute;
            top: 2px;
            left: 2px;
            right: 2px;
            bottom: 2px;
            background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 50%, #fecfef 100%);
            border-radius: 50%;
            z-index: -1;
        }

        .profile-image:hover {
            transform: scale(1.15) rotateY(180deg);
            box-shadow:
                0 20px 50px rgba(255, 154, 158, 0.8),
                0 0 0 20px rgba(255, 154, 158, 0.1),
                0 0 0 40px rgba(255, 154, 158, 0.05);
            animation-play-state: paused;
        }

        @keyframes profilePulse {
            0%, 100% {
                box-shadow: 0 8px 25px rgba(255, 154, 158, 0.4);
                transform: scale(1);
            }
            50% {
                box-shadow: 0 15px 40px rgba(255, 154, 158, 0.8), 0 0 0 15px rgba(255, 154, 158, 0.1);
                transform: scale(1.05);
            }
        }

        @keyframes profileFloat {
            0%, 100% { transform: translateY(0px) rotateZ(0deg); }
            25% { transform: translateY(-5px) rotateZ(1deg); }
            50% { transform: translateY(0px) rotateZ(0deg); }
            75% { transform: translateY(-3px) rotateZ(-1deg); }
        }

        @keyframes profileGlow {
            0% { filter: brightness(1) hue-rotate(0deg); }
            100% { filter: brightness(1.2) hue-rotate(10deg); }
        }

        @keyframes profileSpinBorder {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .name {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 10px;
            position: relative;
            z-index: 1;
            animation: textGlow 3s ease-in-out infinite;
        }

        .title {
            font-size: 1.2rem;
            font-weight: 400;
            opacity: 0.9;
            position: relative;
            z-index: 1;
            animation: titleBounce 2s ease-in-out infinite;
        }

        @keyframes textGlow {
            0%, 100% { text-shadow: 0 0 5px rgba(255, 255, 255, 0.5); }
            50% { text-shadow: 0 0 20px rgba(255, 255, 255, 0.8), 0 0 30px rgba(255, 255, 255, 0.6); }
        }

        @keyframes titleBounce {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-3px); }
        }

        .content {
            padding: 40px;
        }

        .section {
            margin-bottom: 40px;
            animation: slideInLeft 0.6s ease-out;
            animation-fill-mode: both;
        }

        .section:nth-child(2) { animation-delay: 0.2s; }
        .section:nth-child(3) { animation-delay: 0.4s; }

        @keyframes slideInLeft {
            from {
                opacity: 0;
                transform: translateX(-30px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        .section-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: #475569;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .section-title::before {
            content: '';
            width: 4px;
            height: 24px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            border-radius: 2px;
        }

        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 20px;
        }

        .info-card {
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(255, 255, 255, 0.8) 100%);
            padding: 20px;
            border-radius: 20px;
            border: 2px solid transparent;
            background-clip: padding-box;
            transition: all 0.6s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            position: relative;
            overflow: hidden;
            backdrop-filter: blur(15px);
            box-shadow:
                0 8px 32px rgba(102, 126, 234, 0.15),
                inset 0 1px 0 rgba(255, 255, 255, 0.8);
            animation:
                cardFloat 5s ease-in-out infinite,
                cardSlideIn 1.2s cubic-bezier(0.68, -0.55, 0.265, 1.55),
                cardShimmer 3s ease-in-out infinite;
            opacity: 0;
            transform: translateY(60px) rotateX(15deg) scale(0.9);
            animation-fill-mode: forwards;
        }

        .info-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, #667eea, #764ba2, #f093fb, #f5576c, #4facfe, #00f2fe);
            background-size: 400% 400%;
            border-radius: 20px;
            z-index: -2;
            animation: gradientMove 4s ease-in-out infinite;
        }

        .info-card::after {
            content: '';
            position: absolute;
            top: 2px;
            left: 2px;
            right: 2px;
            bottom: 2px;
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(255, 255, 255, 0.8) 100%);
            border-radius: 18px;
            z-index: -1;
        }



        .info-card:hover {
            transform: translateY(-15px) scale(1.05) rotateX(8deg) rotateY(2deg);
            box-shadow:
                0 25px 60px rgba(102, 126, 234, 0.5),
                0 0 0 1px rgba(102, 126, 234, 0.3),
                inset 0 1px 0 rgba(255, 255, 255, 1);
            animation-play-state: paused;
            filter: brightness(1.1);
        }

        .info-card:active {
            transform: scale(0.98) rotateZ(2deg);
            animation: cardPulse 0.8s cubic-bezier(0.68, -0.55, 0.265, 1.55);
        }

        /* Stagger animation delays for cards */
        .info-card:nth-child(1) { animation-delay: 0.1s; }
        .info-card:nth-child(2) { animation-delay: 0.2s; }
        .info-card:nth-child(3) { animation-delay: 0.3s; }
        .info-card:nth-child(4) { animation-delay: 0.4s; }
        .info-card:nth-child(5) { animation-delay: 0.5s; }
        .info-card:nth-child(6) { animation-delay: 0.6s; }
        .info-card:nth-child(7) { animation-delay: 0.7s; }

        @keyframes cardSlideIn {
            0% {
                opacity: 0;
                transform: translateY(60px) rotateX(15deg) scale(0.9);
            }
            60% {
                opacity: 0.8;
                transform: translateY(-10px) rotateX(-2deg) scale(1.02);
            }
            100% {
                opacity: 1;
                transform: translateY(0) rotateX(0) scale(1);
            }
        }

        @keyframes cardFloat {
            0%, 100% { transform: translateY(0px) rotateZ(0deg); }
            25% { transform: translateY(-3px) rotateZ(0.5deg); }
            50% { transform: translateY(0px) rotateZ(0deg); }
            75% { transform: translateY(-2px) rotateZ(-0.5deg); }
        }

        @keyframes cardShimmer {
            0%, 100% { filter: brightness(1) saturate(1); }
            50% { filter: brightness(1.1) saturate(1.2); }
        }

        @keyframes cardPulse {
            0% { transform: scale(1) rotateZ(0deg); }
            25% { transform: scale(0.98) rotateZ(1deg); }
            50% { transform: scale(1.02) rotateZ(0deg); }
            75% { transform: scale(0.99) rotateZ(-1deg); }
            100% { transform: scale(1) rotateZ(0deg); }
        }

        @keyframes gradientMove {
            0%, 100% { background-position: 0% 50%; }
            25% { background-position: 100% 0%; }
            50% { background-position: 100% 100%; }
            75% { background-position: 0% 100%; }
        }

        .info-label {
            font-size: 0.875rem;
            font-weight: 500;
            color: #667eea;
            margin-bottom: 8px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .info-value {
            font-size: 1.1rem;
            font-weight: 500;
            color: #334155;
            line-height: 1.4;
        }

        .education-item {
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(255, 255, 255, 0.7) 100%);
            padding: 24px;
            border-radius: 16px;
            border: 1px solid rgba(245, 87, 108, 0.2);
            margin-bottom: 16px;
            transition: all 0.5s cubic-bezier(0.68, -0.55, 0.265, 1.55);
            position: relative;
            overflow: hidden;
            backdrop-filter: blur(10px);
            box-shadow: 0 4px 15px rgba(245, 87, 108, 0.1);
            animation: educationSlide 3s ease-in-out infinite, educationSlideIn 1s ease-out;
            opacity: 0;
            transform: translateX(-50px) rotateY(-10deg);
            animation-fill-mode: forwards;
        }

        .education-item::before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            width: 5px;
            height: 100%;
            background: linear-gradient(135deg, #f5576c 0%, #f093fb 50%, #764ba2 100%);
            animation: borderPulse 2s ease-in-out infinite;
        }

        .education-item::after {
            content: '';
            position: absolute;
            top: 0;
            right: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(245, 87, 108, 0.1), transparent);
            transition: all 0.5s ease;
        }

        .education-item:hover {
            transform: translateX(12px) scale(1.03) rotateY(2deg);
            box-shadow: 0 20px 40px rgba(245, 87, 108, 0.4);
            border-color: rgba(245, 87, 108, 0.6);
            animation-play-state: paused;
        }

        .education-item:hover::after {
            right: 100%;
        }

        .education-item:active {
            transform: scale(0.98) rotateZ(-1deg);
            animation: educationBounce 0.5s ease-out;
        }

        /* Stagger animation delays for education items */
        .education-item:nth-child(1) { animation-delay: 0.8s; }
        .education-item:nth-child(2) { animation-delay: 1.0s; }
        .education-item:nth-child(3) { animation-delay: 1.2s; }

        @keyframes educationSlideIn {
            0% {
                opacity: 0;
                transform: translateX(-50px) rotateY(-10deg);
            }
            100% {
                opacity: 1;
                transform: translateX(0) rotateY(0);
            }
        }

        @keyframes educationSlide {
            0%, 100% { transform: translateX(0px); }
            50% { transform: translateX(2px); }
        }

        @keyframes educationBounce {
            0% { transform: scale(1); }
            50% { transform: scale(0.98) rotateZ(-1deg); }
            100% { transform: scale(1.01); }
        }

        @keyframes borderPulse {
            0%, 100% { opacity: 1; width: 5px; }
            50% { opacity: 0.7; width: 8px; }
        }

        .education-school {
            font-size: 1.2rem;
            font-weight: 600;
            color: #f5576c;
            margin-bottom: 8px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .education-details {
            color: #64748b;
            font-size: 0.95rem;
            line-height: 1.6;
        }

        .status-badge {
            display: inline-block;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 500;
            margin-top: 8px;
        }

        .status-current {
            background: linear-gradient(135deg, #a7f3d0, #6ee7b7);
            color: #065f46;
            box-shadow: 0 2px 8px rgba(110, 231, 183, 0.3);
        }

        .status-completed {
            background: linear-gradient(135deg, #bfdbfe, #93c5fd);
            color: #1e3a8a;
            box-shadow: 0 2px 8px rgba(147, 197, 253, 0.3);
        }

        /* Enhanced floating particles with multiple layers */
        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-image:
                radial-gradient(3px 3px at 20px 30px, #667eea, transparent),
                radial-gradient(2px 2px at 40px 70px, #764ba2, transparent),
                radial-gradient(1px 1px at 90px 40px, #f093fb, transparent),
                radial-gradient(2px 2px at 130px 80px, #f5576c, transparent),
                radial-gradient(3px 3px at 160px 30px, #4facfe, transparent),
                radial-gradient(1px 1px at 200px 60px, #00f2fe, transparent);
            background-repeat: repeat;
            background-size: 300px 150px;
            animation: particleFloat 25s linear infinite;
            pointer-events: none;
            z-index: 1;
            opacity: 0.7;
        }

        body::after {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-image:
                radial-gradient(2px 2px at 50px 50px, rgba(102, 126, 234, 0.3), transparent),
                radial-gradient(1px 1px at 100px 100px, rgba(118, 75, 162, 0.3), transparent),
                radial-gradient(3px 3px at 150px 25px, rgba(240, 147, 251, 0.3), transparent);
            background-repeat: repeat;
            background-size: 400px 200px;
            animation: particleFloat2 30s linear infinite reverse;
            pointer-events: none;
            z-index: 1;
            opacity: 0.5;
        }

        @keyframes particleFloat {
            0% { transform: translateY(100vh) translateX(0px) rotate(0deg); }
            100% { transform: translateY(-100px) translateX(100px) rotate(360deg); }
        }

        @keyframes particleFloat2 {
            0% { transform: translateY(-100px) translateX(100px) rotate(0deg); }
            100% { transform: translateY(100vh) translateX(-50px) rotate(-360deg); }
        }

        /* Enhanced icon animations */
        .info-label i, .education-school i {
            animation:
                iconFloat 3s ease-in-out infinite,
                iconGlow 2s ease-in-out infinite alternate;
            display: inline-block;
            transition: all 0.4s cubic-bezier(0.68, -0.55, 0.265, 1.55);
            filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
        }

        .info-label i:hover, .education-school i:hover {
            animation: iconBounce 0.8s cubic-bezier(0.68, -0.55, 0.265, 1.55);
            filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.2));
        }

        @keyframes iconFloat {
            0%, 100% { transform: translateY(0px) rotate(0deg) scale(1); }
            25% { transform: translateY(-2px) rotate(3deg) scale(1.05); }
            50% { transform: translateY(0px) rotate(0deg) scale(1); }
            75% { transform: translateY(-1px) rotate(-3deg) scale(1.05); }
        }

        @keyframes iconGlow {
            0% { filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1)) brightness(1); }
            100% { filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.2)) brightness(1.2); }
        }

        @keyframes iconBounce {
            0% { transform: scale(1) rotate(0deg); }
            25% { transform: scale(1.2) rotate(90deg); }
            50% { transform: scale(1.3) rotate(180deg); }
            75% { transform: scale(1.2) rotate(270deg); }
            100% { transform: scale(1) rotate(360deg); }
        }

        /* Section title animation */
        .section-title {
            animation: titleSlideIn 1s ease-out;
            animation-fill-mode: forwards;
        }

        .section:nth-child(1) .section-title { animation-delay: 0.5s; }
        .section:nth-child(2) .section-title { animation-delay: 1.5s; }

        @keyframes titleSlideIn {
            0% {
                opacity: 0;
                transform: translateX(-30px);
            }
            100% {
                opacity: 1;
                transform: translateX(0);
            }
        }

        /* Enhanced status badge animations */
        .status-badge {
            animation: badgePulse 2s ease-in-out infinite;
        }

        @keyframes badgePulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        @media (max-width: 768px) {
            .container {
                margin: 10px;
                border-radius: 16px;
            }

            .header {
                padding: 40px 20px;
            }

            .name {
                font-size: 2rem;
            }

            .content {
                padding: 20px;
            }

            .info-grid {
                grid-template-columns: 1fr;
            }

            /* Reduce animations on mobile for performance */
            body::before {
                animation-duration: 30s;
            }

            .info-card, .education-item {
                animation-duration: 6s;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header Section -->
        <div class="header">
            <div class="profile-image">
                SP
            </div>
            <h1 class="name">Saputra Pramahkota Hati</h1>
            <p class="title">Siswa Kelas 10 TKJ A</p>
        </div>

        <!-- Content Section -->
        <div class="content">
            <!-- Personal Information -->
            <div class="section">
                <h2 class="section-title">Informasi Pribadi</h2>
                <div class="info-grid">
                    <div class="info-card">
                        <div class="info-label">
                            <i class="fas fa-user" style="color: #667eea;"></i>
                            Nama Lengkap
                        </div>
                        <div class="info-value">Saputra Pramahkota Hati</div>
                    </div>
                    <div class="info-card">
                        <div class="info-label">
                            <i class="fas fa-graduation-cap" style="color: #764ba2;"></i>
                            Kelas
                        </div>
                        <div class="info-value">10 TKJ A</div>
                    </div>
                    <div class="info-card">
                        <div class="info-label">
                            <i class="fas fa-calendar" style="color: #f093fb;"></i>
                            Tanggal Lahir
                        </div>
                        <div class="info-value">15 Januari 2008</div>
                    </div>
                    <div class="info-card">
                        <div class="info-label">
                            <i class="fas fa-map-marker-alt" style="color: #f5576c;"></i>
                            Alamat
                        </div>
                        <div class="info-value">Jl. Merdeka No. 123, Kelurahan Sukamaju, Kecamatan Teknologi, Kota Bandung, Jawa Barat 40123</div>
                    </div>
                    <div class="info-card">
                        <div class="info-label">
                            <i class="fas fa-phone" style="color: #4facfe;"></i>
                            No. Telepon
                        </div>
                        <div class="info-value">+62 812-3456-7890</div>
                    </div>
                    <div class="info-card">
                        <div class="info-label">
                            <i class="fas fa-envelope" style="color: #00f2fe;"></i>
                            Email
                        </div>
                        <div class="info-value"><EMAIL></div>
                    </div>
                    <div class="info-card">
                        <div class="info-label">
                            <i class="fas fa-heart" style="color: #ff9a9e;"></i>
                            Hobi
                        </div>
                        <div class="info-value">Programming, Gaming, Membaca, Olahraga</div>
                    </div>
                </div>
            </div>

            <!-- Education History -->
            <div class="section">
                <h2 class="section-title">Riwayat Pendidikan</h2>

                <div class="education-item">
                    <div class="education-school">
                        <i class="fas fa-school" style="color: #667eea;"></i>
                        SMK Negeri 1 Teknologi Bandung
                    </div>
                    <div class="education-details">
                        <strong>Sekolah Menengah Kejuruan</strong><br>
                        <span>Jurusan: Teknik Komputer dan Jaringan (TKJ)</span><br>
                        <span>Periode: 2023 - Sekarang</span><br>
                        <span class="status-badge status-current">Sedang Bersekolah</span>
                    </div>
                </div>

                <div class="education-item">
                    <div class="education-school">
                        <i class="fas fa-school" style="color: #764ba2;"></i>
                        SMP Negeri 5 Bandung
                    </div>
                    <div class="education-details">
                        <strong>Sekolah Menengah Pertama</strong><br>
                        <span>Periode: 2020 - 2023</span><br>
                        <span class="status-badge status-completed">Lulus</span>
                    </div>
                </div>

                <div class="education-item">
                    <div class="education-school">
                        <i class="fas fa-school" style="color: #f093fb;"></i>
                        SD Negeri 10 Sukamaju
                    </div>
                    <div class="education-details">
                        <strong>Sekolah Dasar</strong><br>
                        <span>Periode: 2014 - 2020</span><br>
                        <span class="status-badge status-completed">Lulus</span>
                    </div>
                </div>
            </div>
        </div>
    </div>


</body>
</html>
