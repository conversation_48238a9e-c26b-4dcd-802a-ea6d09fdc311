<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Biodata - Saputra Pramahkota Hati</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="https://unpkg.com/react@18/umd/react.development.js"></script>
    <script src="https://unpkg.com/react-dom@18/umd/react-dom.development.js"></script>
    <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            background: 
                radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.3) 0%, transparent 50%),
                linear-gradient(135deg, #667eea 0%, #764ba2 25%, #f093fb 50%, #f5576c 75%, #4facfe 100%);
            background-size: 400% 400%, 400% 400%, 400% 400%, 400% 400%;
            animation: 
                gradientShift 20s ease-in-out infinite,
                backgroundPulse 8s ease-in-out infinite;
            min-height: 100vh;
            color: #334155;
            overflow-x: hidden;
            scroll-behavior: smooth;
        }

        @keyframes gradientShift {
            0%, 100% { background-position: 0% 50%, 0% 50%, 0% 50%, 0% 50%; }
            25% { background-position: 100% 0%, 50% 100%, 0% 50%, 25% 75%; }
            50% { background-position: 100% 100%, 100% 0%, 100% 100%, 50% 50%; }
            75% { background-position: 0% 100%, 50% 0%, 100% 50%, 75% 25%; }
        }

        @keyframes backgroundPulse {
            0%, 100% { filter: brightness(1) saturate(1) hue-rotate(0deg); }
            50% { filter: brightness(1.1) saturate(1.2) hue-rotate(5deg); }
        }

        #root {
            min-height: 100vh;
            padding: 20px;
        }

        .app-container {
            position: relative;
            min-height: 100vh;
        }

        /* Floating Particles Layer */
        .particles-layer {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-image:
                radial-gradient(4px 4px at 20px 30px, #667eea, transparent),
                radial-gradient(3px 3px at 40px 70px, #764ba2, transparent),
                radial-gradient(2px 2px at 90px 40px, #f093fb, transparent),
                radial-gradient(3px 3px at 130px 80px, #f5576c, transparent),
                radial-gradient(4px 4px at 160px 30px, #4facfe, transparent),
                radial-gradient(2px 2px at 200px 60px, #00f2fe, transparent);
            background-repeat: repeat;
            background-size: 350px 175px;
            animation: 
                particleFloat 30s linear infinite,
                particlePulse 8s ease-in-out infinite;
            pointer-events: none;
            z-index: 1;
            opacity: 0.6;
        }

        @keyframes particleFloat {
            0% { transform: translateY(100vh) translateX(0px) rotate(0deg) scale(1); }
            25% { transform: translateY(75vh) translateX(25px) rotate(90deg) scale(1.2); }
            50% { transform: translateY(50vh) translateX(50px) rotate(180deg) scale(1); }
            75% { transform: translateY(25vh) translateX(75px) rotate(270deg) scale(1.1); }
            100% { transform: translateY(-100px) translateX(100px) rotate(360deg) scale(1); }
        }

        @keyframes particlePulse {
            0%, 100% { opacity: 0.6; filter: brightness(1); }
            50% { opacity: 0.8; filter: brightness(1.3); }
        }

        .container {
            max-width: 900px;
            width: 100%;
            margin: 0 auto;
            background: 
                linear-gradient(135deg, rgba(255, 255, 255, 0.98) 0%, rgba(255, 255, 255, 0.92) 100%);
            backdrop-filter: blur(20px) saturate(180%);
            border-radius: 32px;
            border: 1px solid rgba(255, 255, 255, 0.3);
            box-shadow: 
                0 32px 64px rgba(0, 0, 0, 0.12),
                0 16px 32px rgba(0, 0, 0, 0.08),
                inset 0 1px 0 rgba(255, 255, 255, 0.8);
            overflow: hidden;
            animation: 
                fadeInUp 1.5s cubic-bezier(0.68, -0.55, 0.265, 1.55),
                containerFloat 8s ease-in-out infinite,
                containerGlow 6s ease-in-out infinite;
            position: relative;
            z-index: 10;
            transform-style: preserve-3d;
        }

        /* Scroll Animation Classes */
        .scroll-animate {
            opacity: 0;
            transform: translateY(60px) rotateX(15deg) scale(0.95);
            transition: all 0.8s cubic-bezier(0.68, -0.55, 0.265, 1.55);
        }

        .scroll-animate.animate-in {
            opacity: 1;
            transform: translateY(0) rotateX(0) scale(1);
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes containerFloat {
            0%, 100% { transform: translateY(0px) rotateX(0deg) rotateY(0deg); }
            25% { transform: translateY(-8px) rotateX(1deg) rotateY(0.5deg); }
            50% { transform: translateY(0px) rotateX(0deg) rotateY(0deg); }
            75% { transform: translateY(-4px) rotateX(-0.5deg) rotateY(-0.5deg); }
        }

        @keyframes containerGlow {
            0%, 100% { 
                box-shadow: 
                    0 32px 64px rgba(0, 0, 0, 0.12),
                    0 16px 32px rgba(0, 0, 0, 0.08),
                    inset 0 1px 0 rgba(255, 255, 255, 0.8);
            }
            50% { 
                box-shadow: 
                    0 40px 80px rgba(102, 126, 234, 0.2),
                    0 20px 40px rgba(102, 126, 234, 0.15),
                    inset 0 1px 0 rgba(255, 255, 255, 1);
            }
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
            color: white;
            padding: 60px 40px;
            text-align: center;
            position: relative;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
        }

        .profile-image {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            margin: 0 auto 20px;
            background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 50%, #fecfef 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 48px;
            color: #667eea;
            border: 4px solid rgba(255, 255, 255, 0.5);
            position: relative;
            z-index: 1;
            font-weight: 700;
            box-shadow: 0 8px 25px rgba(255, 154, 158, 0.4);
            animation: 
                profilePulse 3s ease-in-out infinite,
                profileFloat 4s ease-in-out infinite;
        }

        @keyframes profilePulse {
            0%, 100% { 
                box-shadow: 0 8px 25px rgba(255, 154, 158, 0.4);
                transform: scale(1);
            }
            50% { 
                box-shadow: 0 15px 40px rgba(255, 154, 158, 0.8), 0 0 0 15px rgba(255, 154, 158, 0.1);
                transform: scale(1.05);
            }
        }

        @keyframes profileFloat {
            0%, 100% { transform: translateY(0px) rotateZ(0deg); }
            25% { transform: translateY(-5px) rotateZ(1deg); }
            50% { transform: translateY(0px) rotateZ(0deg); }
            75% { transform: translateY(-3px) rotateZ(-1deg); }
        }

        .name {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 10px;
            position: relative;
            z-index: 1;
            animation: textGlow 4s ease-in-out infinite;
        }

        .title {
            font-size: 1.2rem;
            font-weight: 400;
            opacity: 0.95;
            position: relative;
            z-index: 1;
            animation: titleBounce 4s ease-in-out infinite;
        }

        @keyframes textGlow {
            0%, 100% { text-shadow: 0 0 5px rgba(255, 255, 255, 0.5); }
            50% { text-shadow: 0 0 20px rgba(255, 255, 255, 0.8), 0 0 30px rgba(255, 255, 255, 0.6); }
        }

        @keyframes titleBounce {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-3px); }
        }

        .content {
            padding: 40px;
        }

        .section {
            margin-bottom: 40px;
            animation: 
                sectionFloat 8s ease-in-out infinite;
        }

        @keyframes sectionFloat {
            0%, 100% { transform: translateY(0px) rotateZ(0deg); }
            25% { transform: translateY(-3px) rotateZ(0.3deg); }
            50% { transform: translateY(0px) rotateZ(0deg); }
            75% { transform: translateY(-2px) rotateZ(-0.3deg); }
        }

        .section-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: #475569;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
            position: relative;
            animation: 
                titleFloat 6s ease-in-out infinite;
        }

        .section-title i {
            color: #667eea;
            animation: iconFloat 3s ease-in-out infinite;
        }

        @keyframes titleFloat {
            0%, 100% { transform: translateY(0px) rotateZ(0deg); }
            50% { transform: translateY(-2px) rotateZ(0.5deg); }
        }

        @keyframes iconFloat {
            0%, 100% { transform: translateY(0px) rotate(0deg) scale(1); }
            25% { transform: translateY(-2px) rotate(3deg) scale(1.05); }
            50% { transform: translateY(0px) rotate(0deg) scale(1); }
            75% { transform: translateY(-1px) rotate(-3deg) scale(1.05); }
        }

        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 20px;
        }

        .info-card {
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(255, 255, 255, 0.8) 100%);
            padding: 20px;
            border-radius: 20px;
            border: 2px solid transparent;
            transition: all 0.6s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            position: relative;
            overflow: hidden;
            backdrop-filter: blur(15px);
            box-shadow: 
                0 8px 32px rgba(102, 126, 234, 0.15),
                inset 0 1px 0 rgba(255, 255, 255, 0.8);
            animation: cardFloat 5s ease-in-out infinite;
        }

        .info-card:hover {
            transform: translateY(-15px) scale(1.05) rotateX(8deg) rotateY(2deg);
            box-shadow: 
                0 25px 60px rgba(102, 126, 234, 0.5),
                0 0 0 1px rgba(102, 126, 234, 0.3),
                inset 0 1px 0 rgba(255, 255, 255, 1);
            filter: brightness(1.1);
        }

        @keyframes cardFloat {
            0%, 100% { transform: translateY(0px) rotateZ(0deg); }
            25% { transform: translateY(-3px) rotateZ(0.5deg); }
            50% { transform: translateY(0px) rotateZ(0deg); }
            75% { transform: translateY(-2px) rotateZ(-0.5deg); }
        }

        .info-label {
            font-size: 0.875rem;
            font-weight: 500;
            color: #667eea;
            margin-bottom: 8px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .info-label i {
            animation: iconFloat 3s ease-in-out infinite;
        }

        .info-value {
            font-size: 1.1rem;
            font-weight: 500;
            color: #334155;
            line-height: 1.4;
        }

        .education-item {
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(255, 255, 255, 0.7) 100%);
            padding: 24px;
            border-radius: 16px;
            border: 1px solid rgba(245, 87, 108, 0.2);
            margin-bottom: 16px;
            transition: all 0.5s cubic-bezier(0.68, -0.55, 0.265, 1.55);
            position: relative;
            overflow: hidden;
            backdrop-filter: blur(10px);
            box-shadow: 0 4px 15px rgba(245, 87, 108, 0.1);
            animation: educationSlide 3s ease-in-out infinite;
        }

        .education-item::before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            width: 5px;
            height: 100%;
            background: linear-gradient(135deg, #f5576c 0%, #f093fb 50%, #764ba2 100%);
            animation: borderPulse 2s ease-in-out infinite;
        }

        .education-item:hover {
            transform: translateX(12px) scale(1.03) rotateY(2deg);
            box-shadow: 0 20px 40px rgba(245, 87, 108, 0.4);
            border-color: rgba(245, 87, 108, 0.6);
        }

        @keyframes educationSlide {
            0%, 100% { transform: translateX(0px); }
            50% { transform: translateX(2px); }
        }

        @keyframes borderPulse {
            0%, 100% { opacity: 1; width: 5px; }
            50% { opacity: 0.7; width: 8px; }
        }

        .education-school {
            font-size: 1.2rem;
            font-weight: 600;
            color: #f5576c;
            margin-bottom: 8px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .education-school i {
            animation: iconFloat 3s ease-in-out infinite;
        }

        .education-details {
            color: #64748b;
            font-size: 0.95rem;
            line-height: 1.6;
        }

        .status-badge {
            display: inline-block;
            padding: 6px 16px;
            border-radius: 25px;
            font-size: 0.85rem;
            font-weight: 600;
            margin-top: 8px;
            animation: badgePulse 3s ease-in-out infinite;
        }

        .status-current {
            background: linear-gradient(135deg, #a7f3d0, #6ee7b7);
            color: #065f46;
            box-shadow: 0 4px 12px rgba(110, 231, 183, 0.4);
        }

        .status-completed {
            background: linear-gradient(135deg, #bfdbfe, #93c5fd);
            color: #1e3a8a;
            box-shadow: 0 4px 12px rgba(147, 197, 253, 0.4);
        }

        @keyframes badgePulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.02); }
        }

        @media (max-width: 768px) {
            .container {
                margin: 10px;
                border-radius: 16px;
            }
            
            .header {
                padding: 40px 20px;
            }
            
            .name {
                font-size: 2rem;
            }
            
            .content {
                padding: 20px;
            }
            
            .info-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div id="root"></div>

    <script type="text/babel">
        const { useState, useEffect } = React;

        function App() {
            const personalInfo = {
                name: "Saputra Pramahkota Hati",
                class: "10 TKJ A",
                address: "Jl. Merdeka No. 123, Kelurahan Sukamaju, Kecamatan Teknologi, Kota Bandung, Jawa Barat 40123",
                phone: "+62 812-3456-7890",
                email: "<EMAIL>",
                birthDate: "15 Januari 2008",
                hobby: "Programming, Gaming, Membaca, Olahraga"
            };

            const educationHistory = [
                {
                    school: "SMK Negeri 1 Teknologi Bandung",
                    level: "Sekolah Menengah Kejuruan",
                    major: "Teknik Komputer dan Jaringan (TKJ)",
                    period: "2023 - Sekarang",
                    status: "Sedang Bersekolah"
                },
                {
                    school: "SMP Negeri 5 Bandung",
                    level: "Sekolah Menengah Pertama",
                    major: "-",
                    period: "2020 - 2023",
                    status: "Lulus"
                },
                {
                    school: "SD Negeri 10 Sukamaju",
                    level: "Sekolah Dasar",
                    major: "-",
                    period: "2014 - 2020",
                    status: "Lulus"
                }
            ];

            useEffect(() => {
                // Scroll animations
                const observerOptions = {
                    threshold: 0.1,
                    rootMargin: '0px 0px -50px 0px'
                };

                const observer = new IntersectionObserver((entries) => {
                    entries.forEach((entry) => {
                        if (entry.isIntersecting) {
                            entry.target.classList.add('animate-in');
                        } else {
                            entry.target.classList.remove('animate-in');
                        }
                    });
                }, observerOptions);

                // Observe all animatable elements
                const animatableElements = document.querySelectorAll('.info-card, .education-item, .section-title, .header');
                animatableElements.forEach(el => observer.observe(el));

                // Parallax scroll effect
                const handleScroll = () => {
                    const scrolled = window.pageYOffset;
                    const header = document.querySelector('.header');
                    const particles = document.querySelector('.particles-layer');
                    
                    if (header) {
                        header.style.transform = `translateY(${scrolled * 0.3}px)`;
                    }
                    if (particles) {
                        particles.style.transform = `translateY(${scrolled * 0.1}px) rotate(${scrolled * 0.1}deg)`;
                    }
                };

                window.addEventListener('scroll', handleScroll);

                return () => {
                    observer.disconnect();
                    window.removeEventListener('scroll', handleScroll);
                };
            }, []);

            return (
                <div className="app-container">
                    {/* Floating Particles Layer */}
                    <div className="particles-layer"></div>
                    
                    <div className="container scroll-animate">
                        {/* Header Section */}
                        <div className="header scroll-animate">
                            <div className="profile-image">
                                SP
                            </div>
                            <h1 className="name">{personalInfo.name}</h1>
                            <p className="title">Siswa Kelas {personalInfo.class}</p>
                        </div>

                        {/* Content Section */}
                        <div className="content">
                            {/* Personal Information */}
                            <div className="section scroll-animate">
                                <h2 className="section-title scroll-animate">
                                    <i className="fas fa-user-circle"></i>
                                    Informasi Pribadi
                                </h2>
                                <div className="info-grid">
                                    <div className="info-card scroll-animate">
                                        <div className="info-label">
                                            <i className="fas fa-user" style={{color: '#667eea'}}></i>
                                            Nama Lengkap
                                        </div>
                                        <div className="info-value">{personalInfo.name}</div>
                                    </div>
                                    <div className="info-card scroll-animate">
                                        <div className="info-label">
                                            <i className="fas fa-graduation-cap" style={{color: '#764ba2'}}></i>
                                            Kelas
                                        </div>
                                        <div className="info-value">{personalInfo.class}</div>
                                    </div>
                                    <div className="info-card scroll-animate">
                                        <div className="info-label">
                                            <i className="fas fa-calendar" style={{color: '#f093fb'}}></i>
                                            Tanggal Lahir
                                        </div>
                                        <div className="info-value">{personalInfo.birthDate}</div>
                                    </div>
                                    <div className="info-card scroll-animate">
                                        <div className="info-label">
                                            <i className="fas fa-map-marker-alt" style={{color: '#f5576c'}}></i>
                                            Alamat
                                        </div>
                                        <div className="info-value">{personalInfo.address}</div>
                                    </div>
                                    <div className="info-card scroll-animate">
                                        <div className="info-label">
                                            <i className="fas fa-phone" style={{color: '#4facfe'}}></i>
                                            No. Telepon
                                        </div>
                                        <div className="info-value">{personalInfo.phone}</div>
                                    </div>
                                    <div className="info-card scroll-animate">
                                        <div className="info-label">
                                            <i className="fas fa-envelope" style={{color: '#00f2fe'}}></i>
                                            Email
                                        </div>
                                        <div className="info-value">{personalInfo.email}</div>
                                    </div>
                                    <div className="info-card scroll-animate">
                                        <div className="info-label">
                                            <i className="fas fa-heart" style={{color: '#ff9a9e'}}></i>
                                            Hobi
                                        </div>
                                        <div className="info-value">{personalInfo.hobby}</div>
                                    </div>
                                </div>
                            </div>

                            {/* Education History */}
                            <div className="section scroll-animate">
                                <h2 className="section-title scroll-animate">
                                    <i className="fas fa-school"></i>
                                    Riwayat Pendidikan
                                </h2>
                                {educationHistory.map((education, index) => (
                                    <div key={index} className="education-item scroll-animate">
                                        <div className="education-school">
                                            <i className="fas fa-school" style={{color: index === 0 ? '#667eea' : index === 1 ? '#764ba2' : '#f093fb'}}></i>
                                            {education.school}
                                        </div>
                                        <div className="education-details">
                                            <strong>{education.level}</strong>
                                            {education.major !== "-" && (
                                                <>
                                                    <br />
                                                    <span>Jurusan: {education.major}</span>
                                                </>
                                            )}
                                            <br />
                                            <span>Periode: {education.period}</span>
                                            <br />
                                            <span className={`status-badge ${education.status === 'Sedang Bersekolah' ? 'status-current' : 'status-completed'}`}>
                                                {education.status}
                                            </span>
                                        </div>
                                    </div>
                                ))}
                            </div>
                        </div>
                    </div>
                </div>
            );
        }

        ReactDOM.render(<App />, document.getElementById('root'));
    </script>
</body>
</html>
