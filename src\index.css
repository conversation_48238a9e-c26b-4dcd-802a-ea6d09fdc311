* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background:
    radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.3) 0%, transparent 50%),
    linear-gradient(135deg, #667eea 0%, #764ba2 25%, #f093fb 50%, #f5576c 75%, #4facfe 100%);
  background-size: 400% 400%, 400% 400%, 400% 400%, 400% 400%;
  animation:
    gradientShift 20s ease-in-out infinite,
    backgroundPulse 8s ease-in-out infinite;
  min-height: 100vh;
  color: #334155;
  overflow-x: hidden;
  scroll-behavior: smooth;
}

@keyframes gradientShift {
  0%, 100% { background-position: 0% 50%, 0% 50%, 0% 50%, 0% 50%; }
  25% { background-position: 100% 0%, 50% 100%, 0% 50%, 25% 75%; }
  50% { background-position: 100% 100%, 100% 0%, 100% 100%, 50% 50%; }
  75% { background-position: 0% 100%, 50% 0%, 100% 50%, 75% 25%; }
}

@keyframes backgroundPulse {
  0%, 100% { filter: brightness(1) saturate(1) hue-rotate(0deg); }
  50% { filter: brightness(1.1) saturate(1.2) hue-rotate(5deg); }
}

#root {
  min-height: 100vh;
  padding: 20px;
}

.app-container {
  position: relative;
  min-height: 100vh;
}

/* Floating Particles Layer */
.particles-layer {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image:
    radial-gradient(4px 4px at 20px 30px, #667eea, transparent),
    radial-gradient(3px 3px at 40px 70px, #764ba2, transparent),
    radial-gradient(2px 2px at 90px 40px, #f093fb, transparent),
    radial-gradient(3px 3px at 130px 80px, #f5576c, transparent),
    radial-gradient(4px 4px at 160px 30px, #4facfe, transparent),
    radial-gradient(2px 2px at 200px 60px, #00f2fe, transparent);
  background-repeat: repeat;
  background-size: 350px 175px;
  animation:
    particleFloat 30s linear infinite,
    particlePulse 8s ease-in-out infinite;
  pointer-events: none;
  z-index: 1;
  opacity: 0.6;
}

@keyframes particleFloat {
  0% { transform: translateY(100vh) translateX(0px) rotate(0deg) scale(1); }
  25% { transform: translateY(75vh) translateX(25px) rotate(90deg) scale(1.2); }
  50% { transform: translateY(50vh) translateX(50px) rotate(180deg) scale(1); }
  75% { transform: translateY(25vh) translateX(75px) rotate(270deg) scale(1.1); }
  100% { transform: translateY(-100px) translateX(100px) rotate(360deg) scale(1); }
}

@keyframes particlePulse {
  0%, 100% { opacity: 0.6; filter: brightness(1); }
  50% { opacity: 0.8; filter: brightness(1.3); }
}

.container {
  max-width: 900px;
  width: 100%;
  margin: 0 auto;
  background:
    linear-gradient(135deg, rgba(255, 255, 255, 0.98) 0%, rgba(255, 255, 255, 0.92) 100%);
  backdrop-filter: blur(20px) saturate(180%);
  border-radius: 32px;
  border: 1px solid rgba(255, 255, 255, 0.3);
  box-shadow:
    0 32px 64px rgba(0, 0, 0, 0.12),
    0 16px 32px rgba(0, 0, 0, 0.08),
    inset 0 1px 0 rgba(255, 255, 255, 0.8);
  overflow: hidden;
  animation:
    fadeInUp 1.5s cubic-bezier(0.68, -0.55, 0.265, 1.55),
    containerFloat 8s ease-in-out infinite,
    containerGlow 6s ease-in-out infinite;
  position: relative;
  z-index: 10;
  transform-style: preserve-3d;
}

/* Scroll Animation Classes */
.scroll-animate {
  opacity: 0;
  transform: translateY(60px) rotateX(15deg) scale(0.95);
  transition: all 0.8s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

.scroll-animate.animate-in {
  opacity: 1;
  transform: translateY(0) rotateX(0) scale(1);
}

/* Stagger delays for scroll animations */
.scroll-animate[data-delay="0.1"] { transition-delay: 0.1s; }
.scroll-animate[data-delay="0.2"] { transition-delay: 0.2s; }
.scroll-animate[data-delay="0.3"] { transition-delay: 0.3s; }
.scroll-animate[data-delay="0.4"] { transition-delay: 0.4s; }
.scroll-animate[data-delay="0.5"] { transition-delay: 0.5s; }
.scroll-animate[data-delay="0.6"] { transition-delay: 0.6s; }
.scroll-animate[data-delay="0.7"] { transition-delay: 0.7s; }

@keyframes containerFloat {
  0%, 100% { transform: translateY(0px) rotateX(0deg) rotateY(0deg); }
  25% { transform: translateY(-8px) rotateX(1deg) rotateY(0.5deg); }
  50% { transform: translateY(0px) rotateX(0deg) rotateY(0deg); }
  75% { transform: translateY(-4px) rotateX(-0.5deg) rotateY(-0.5deg); }
}

@keyframes containerGlow {
  0%, 100% {
    box-shadow:
      0 32px 64px rgba(0, 0, 0, 0.12),
      0 16px 32px rgba(0, 0, 0, 0.08),
      inset 0 1px 0 rgba(255, 255, 255, 0.8);
  }
  50% {
    box-shadow:
      0 40px 80px rgba(102, 126, 234, 0.2),
      0 20px 40px rgba(102, 126, 234, 0.15),
      inset 0 1px 0 rgba(255, 255, 255, 1);
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.header {
  background: linear-gradient(135deg, #64748b 0%, #94a3b8 100%);
  color: white;
  padding: 60px 40px;
  text-align: center;
  position: relative;
  overflow: hidden;
}

.header::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
  animation: float 6s ease-in-out infinite;
}

@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-20px) rotate(180deg); }
}

.profile-image {
  width: 120px;
  height: 120px;
  border-radius: 50%;
  margin: 0 auto 20px;
  background: linear-gradient(135deg, #cbd5e1, #e2e8f0);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 48px;
  color: #64748b;
  border: 4px solid rgba(255, 255, 255, 0.3);
  position: relative;
  z-index: 1;
}

.name {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 10px;
  position: relative;
  z-index: 1;
}

.title {
  font-size: 1.2rem;
  font-weight: 400;
  opacity: 0.9;
  position: relative;
  z-index: 1;
}

.content {
  padding: 40px;
}

.section {
  margin-bottom: 40px;
  animation: slideInLeft 0.6s ease-out;
  animation-fill-mode: both;
}

.section:nth-child(2) { animation-delay: 0.2s; }
.section:nth-child(3) { animation-delay: 0.4s; }

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.section-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: #475569;
  margin-bottom: 20px;
  display: flex;
  align-items: center;
  gap: 10px;
  position: relative;
  animation:
    titleFloat 6s ease-in-out infinite,
    titleGlow 4s ease-in-out infinite;
}

.section-title i {
  animation:
    iconFloat 3s ease-in-out infinite,
    iconGlow 2s ease-in-out infinite alternate;
  display: inline-block;
  transition: all 0.4s cubic-bezier(0.68, -0.55, 0.265, 1.55);
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
  color: #667eea;
}

.section-title i:hover {
  animation: iconBounce 0.8s cubic-bezier(0.68, -0.55, 0.265, 1.55);
  filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.2));
}

.section-title::before {
  content: '';
  width: 6px;
  height: 28px;
  background: linear-gradient(135deg, #667eea, #764ba2, #f093fb);
  border-radius: 3px;
  animation:
    barPulse 3s ease-in-out infinite,
    barShimmer 2s ease-in-out infinite;
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
}

.section-title::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 100%;
  height: 2px;
  background: linear-gradient(90deg, #667eea, #764ba2, #f093fb);
  animation: underlineGlow 3s ease-in-out infinite;
}

@keyframes titleFloat {
  0%, 100% { transform: translateY(0px) rotateZ(0deg); }
  50% { transform: translateY(-2px) rotateZ(0.5deg); }
}

@keyframes titleGlow {
  0%, 100% {
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    filter: brightness(1);
  }
  50% {
    text-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
    filter: brightness(1.1);
  }
}

@keyframes barPulse {
  0%, 100% { transform: scaleY(1) scaleX(1); }
  50% { transform: scaleY(1.1) scaleX(1.2); }
}

@keyframes barShimmer {
  0%, 100% { box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3); }
  50% { box-shadow: 0 4px 16px rgba(102, 126, 234, 0.6); }
}

@keyframes underlineGlow {
  0%, 100% { opacity: 0.5; }
  50% { opacity: 1; }
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
}

.info-card {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(255, 255, 255, 0.8) 100%);
  padding: 20px;
  border-radius: 20px;
  border: 2px solid transparent;
  background-clip: padding-box;
  transition: all 0.6s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  position: relative;
  overflow: hidden;
  backdrop-filter: blur(15px);
  box-shadow:
    0 8px 32px rgba(102, 126, 234, 0.15),
    inset 0 1px 0 rgba(255, 255, 255, 0.8);
  animation:
    cardFloat 5s ease-in-out infinite,
    cardShimmer 3s ease-in-out infinite;
}

.info-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, #667eea, #764ba2, #f093fb, #f5576c, #4facfe, #00f2fe);
  background-size: 400% 400%;
  border-radius: 20px;
  z-index: -2;
  animation: gradientMove 4s ease-in-out infinite;
}

.info-card::after {
  content: '';
  position: absolute;
  top: 2px;
  left: 2px;
  right: 2px;
  bottom: 2px;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(255, 255, 255, 0.8) 100%);
  border-radius: 18px;
  z-index: -1;
}

.info-card:hover {
  transform: translateY(-15px) scale(1.05) rotateX(8deg) rotateY(2deg);
  box-shadow:
    0 25px 60px rgba(102, 126, 234, 0.5),
    0 0 0 1px rgba(102, 126, 234, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 1);
  animation-play-state: paused;
  filter: brightness(1.1);
}

.info-card:active {
  transform: scale(0.98) rotateZ(2deg);
  animation: cardPulse 0.8s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

@keyframes cardFloat {
  0%, 100% { transform: translateY(0px) rotateZ(0deg); }
  25% { transform: translateY(-3px) rotateZ(0.5deg); }
  50% { transform: translateY(0px) rotateZ(0deg); }
  75% { transform: translateY(-2px) rotateZ(-0.5deg); }
}

@keyframes cardShimmer {
  0%, 100% { filter: brightness(1) saturate(1); }
  50% { filter: brightness(1.1) saturate(1.2); }
}

@keyframes cardPulse {
  0% { transform: scale(1) rotateZ(0deg); }
  25% { transform: scale(0.98) rotateZ(1deg); }
  50% { transform: scale(1.02) rotateZ(0deg); }
  75% { transform: scale(0.99) rotateZ(-1deg); }
  100% { transform: scale(1) rotateZ(0deg); }
}

@keyframes gradientMove {
  0%, 100% { background-position: 0% 50%; }
  25% { background-position: 100% 0%; }
  50% { background-position: 100% 100%; }
  75% { background-position: 0% 100%; }
}

.info-label {
  font-size: 0.875rem;
  font-weight: 500;
  color: #667eea;
  margin-bottom: 8px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.info-label i {
  animation:
    iconFloat 3s ease-in-out infinite,
    iconGlow 2s ease-in-out infinite alternate;
  display: inline-block;
  transition: all 0.4s cubic-bezier(0.68, -0.55, 0.265, 1.55);
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
}

.info-label i:hover {
  animation: iconBounce 0.8s cubic-bezier(0.68, -0.55, 0.265, 1.55);
  filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.2));
}

@keyframes iconFloat {
  0%, 100% { transform: translateY(0px) rotate(0deg) scale(1); }
  25% { transform: translateY(-2px) rotate(3deg) scale(1.05); }
  50% { transform: translateY(0px) rotate(0deg) scale(1); }
  75% { transform: translateY(-1px) rotate(-3deg) scale(1.05); }
}

@keyframes iconGlow {
  0% { filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1)) brightness(1); }
  100% { filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.2)) brightness(1.2); }
}

@keyframes iconBounce {
  0% { transform: scale(1) rotate(0deg); }
  25% { transform: scale(1.2) rotate(90deg); }
  50% { transform: scale(1.3) rotate(180deg); }
  75% { transform: scale(1.2) rotate(270deg); }
  100% { transform: scale(1) rotate(360deg); }
}

.info-value {
  font-size: 1.1rem;
  font-weight: 500;
  color: #334155;
}

.education-item {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(255, 255, 255, 0.7) 100%);
  padding: 24px;
  border-radius: 16px;
  border: 1px solid rgba(245, 87, 108, 0.2);
  margin-bottom: 16px;
  transition: all 0.5s cubic-bezier(0.68, -0.55, 0.265, 1.55);
  position: relative;
  overflow: hidden;
  backdrop-filter: blur(10px);
  box-shadow: 0 4px 15px rgba(245, 87, 108, 0.1);
  animation:
    educationSlide 3s ease-in-out infinite,
    educationGlow 4s ease-in-out infinite;
}

.education-item::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  width: 5px;
  height: 100%;
  background: linear-gradient(135deg, #f5576c 0%, #f093fb 50%, #764ba2 100%);
  animation: borderPulse 2s ease-in-out infinite;
}

.education-item::after {
  content: '';
  position: absolute;
  top: 0;
  right: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(245, 87, 108, 0.1), transparent);
  transition: all 0.5s ease;
}

.education-item:hover {
  transform: translateX(12px) scale(1.03) rotateY(2deg);
  box-shadow: 0 20px 40px rgba(245, 87, 108, 0.4);
  border-color: rgba(245, 87, 108, 0.6);
  animation-play-state: paused;
}

.education-item:hover::after {
  right: 100%;
}

.education-item:active {
  transform: scale(0.98) rotateZ(-1deg);
  animation: educationBounce 0.5s ease-out;
}

@keyframes educationSlide {
  0%, 100% { transform: translateX(0px); }
  50% { transform: translateX(2px); }
}

@keyframes educationGlow {
  0%, 100% { filter: brightness(1) saturate(1); }
  50% { filter: brightness(1.05) saturate(1.1); }
}

@keyframes educationBounce {
  0% { transform: scale(1); }
  50% { transform: scale(0.98) rotateZ(-1deg); }
  100% { transform: scale(1.01); }
}

@keyframes borderPulse {
  0%, 100% { opacity: 1; width: 5px; }
  50% { opacity: 0.7; width: 8px; }
}

.education-school {
  font-size: 1.2rem;
  font-weight: 600;
  color: #f5576c;
  margin-bottom: 8px;
  display: flex;
  align-items: center;
  gap: 10px;
}

.education-school i {
  animation:
    iconFloat 3s ease-in-out infinite,
    iconGlow 2s ease-in-out infinite alternate;
  display: inline-block;
  transition: all 0.4s cubic-bezier(0.68, -0.55, 0.265, 1.55);
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
}

.education-school i:hover {
  animation: iconBounce 0.8s cubic-bezier(0.68, -0.55, 0.265, 1.55);
  filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.2));
}

.education-details {
  color: #64748b;
  font-size: 0.95rem;
  line-height: 1.5;
}

.status-badge {
  display: inline-block;
  padding: 6px 16px;
  border-radius: 25px;
  font-size: 0.85rem;
  font-weight: 600;
  margin-top: 8px;
  animation:
    badgePulse 3s ease-in-out infinite,
    badgeGlow 2s ease-in-out infinite;
  transition: all 0.3s ease;
}

.status-current {
  background: linear-gradient(135deg, #a7f3d0, #6ee7b7);
  color: #065f46;
  box-shadow: 0 4px 12px rgba(110, 231, 183, 0.4);
}

.status-completed {
  background: linear-gradient(135deg, #bfdbfe, #93c5fd);
  color: #1e3a8a;
  box-shadow: 0 4px 12px rgba(147, 197, 253, 0.4);
}

.status-badge:hover {
  transform: scale(1.05) translateY(-2px);
  animation-play-state: paused;
}

@keyframes badgePulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.02); }
}

@keyframes badgeGlow {
  0%, 100% { filter: brightness(1); }
  50% { filter: brightness(1.1); }
}

@media (max-width: 768px) {
  .container {
    margin: 10px;
    border-radius: 16px;
  }

  .header {
    padding: 40px 20px;
  }

  .name {
    font-size: 2rem;
  }

  .content {
    padding: 20px;
  }

  .info-grid {
    grid-template-columns: 1fr;
  }

  /* Reduce animations on mobile for performance */
  .particles-layer {
    animation-duration: 40s;
  }

  .info-card, .education-item {
    animation-duration: 6s;
  }
}
